#!/bin/bash

# Album Art Proxy Setup Script
# This script helps you set up the album art proxy for your ESPHome media screen

echo "🎵 ESPHome Album Art Proxy Setup"
echo "================================"

# Check if Python 3 is installed
if ! command -v python3 &> /dev/null; then
    echo "❌ Python 3 is not installed. Please install Python 3 first."
    exit 1
fi

echo "✅ Python 3 found"

# Create virtual environment if it doesn't exist
if [ ! -d "venv" ]; then
    echo "📦 Creating Python virtual environment..."
    python3 -m venv venv
fi

# Activate virtual environment
echo "🔧 Activating virtual environment..."
source venv/bin/activate

# Install requirements
echo "📥 Installing Python dependencies..."
pip install -r requirements.txt

echo ""
echo "⚙️  Configuration Required:"
echo "1. Edit album_art_proxy.py and update the following variables:"
echo "   - HA_URL: Your Home Assistant URL (e.g., http://************:8123)"
echo "   - HA_TOKEN: Your Home Assistant Long-Lived Access Token"
echo "   - MEDIA_PLAYER_ENTITY: Your media player entity ID (currently: media_player.escritorio)"
echo ""
echo "2. Edit media-screen-escritorio.yml and update the album art URL:"
echo "   - Change 'http://*************:5052/album_art' to your server's IP"
echo ""
echo "📋 To get a Home Assistant Long-Lived Access Token:"
echo "   1. Go to Home Assistant → Profile → Long-Lived Access Tokens"
echo "   2. Click 'Create Token'"
echo "   3. Give it a name like 'ESPHome Album Art'"
echo "   4. Copy the token and paste it in album_art_proxy.py"
echo ""
echo "🚀 After configuration, run:"
echo "   ./run_proxy.sh"
echo ""
echo "🔍 Test the proxy:"
echo "   curl http://localhost:5052/status"
echo "   curl http://localhost:5052/album_art -o test_album_art.jpg"
