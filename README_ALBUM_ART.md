# ESPHome Album Art Display

This project adds album art display functionality to your ESPHome media screen device. Since ESP32 devices have limited resources for processing large PNG images, this solution uses a Python proxy server to fetch, resize, and serve album art images.

## 🏗️ Architecture

```
Home Assistant → Python Proxy Server → ESPHome Device
                      ↓
                 Resizes images to 80x80px
                 Caches for performance
                 Serves via HTTP
```

## 📁 Files

- `album_art_proxy.py` - Python server that fetches and resizes album art
- `media-screen-escritorio.yml` - Updated ESPHome configuration with album art support
- `requirements.txt` - Python dependencies
- `setup_album_art.sh` - Setup script
- `run_proxy.sh` - <PERSON>ript to run the proxy server

## 🚀 Quick Setup

### 1. Run Setup Script
```bash
./setup_album_art.sh
```

### 2. Configure Home Assistant Access

Edit `album_art_proxy.py` and update:
```python
HA_URL = "http://your-homeassistant-ip:8123"  # Your HA URL
HA_TOKEN = "your-long-lived-access-token"    # Your HA token
```

**To get a Long-Lived Access Token:**
1. Go to Home Assistant → Profile → Long-Lived Access Tokens
2. Click "Create Token"
3. Name it "ESPHome Album Art"
4. Copy the token

### 3. Update ESPHome Configuration

In `media-screen-escritorio.yml`, update the album art URL:
```yaml
image:
  - file: "http://YOUR-SERVER-IP:5052/album_art"  # Replace with your server's IP
    id: album_art
    type: RGB24
    resize: 80x80
```

### 4. Start the Proxy Server
```bash
./run_proxy.sh
```

### 5. Upload ESPHome Configuration
```bash
esphome run media-screen-escritorio.yml
```

## 🎛️ Configuration Options

### Proxy Server Settings (album_art_proxy.py)
- `IMAGE_SIZE`: Album art dimensions (default: 80x80)
- `CACHE_DURATION`: How long to cache images in seconds (default: 300)
- `SERVER_PORT`: HTTP server port (default: 5052)
- `MEDIA_PLAYER_ENTITY`: Your media player entity ID

### ESPHome Display Layout
The display now shows:
- Album art (80x80px) on the left side
- Song title, artist, album on the right
- Progress bar below
- Control buttons (play/pause, prev/next)
- Volume controls at the bottom

## 🔧 Testing

### Test the proxy server:
```bash
# Check server status
curl http://localhost:5052/status

# Download current album art
curl http://localhost:5052/album_art -o test_album_art.jpg
```

### Check ESPHome logs:
```bash
esphome logs media-screen-escritorio.yml
```

## 🐛 Troubleshooting

### Album art not showing:
1. Check proxy server logs for errors
2. Verify Home Assistant token is correct
3. Ensure media player has album art available
4. Check network connectivity between ESP32 and proxy server

### Image loading errors:
- The proxy creates a placeholder image when no album art is available
- Check Home Assistant media player entity for `entity_picture` attribute
- Verify the media player is actually playing content with album art

### Performance issues:
- Images are cached for 5 minutes by default
- Album art updates every 30 seconds when playing
- Adjust `CACHE_DURATION` and update interval as needed

## 🎨 Customization

### Change album art size:
1. Update `IMAGE_SIZE` in `album_art_proxy.py`
2. Update `resize:` in ESPHome config
3. Adjust display layout coordinates in the lambda function

### Different media player:
Update `MEDIA_PLAYER_ENTITY` in both:
- `album_art_proxy.py`
- `media-screen-escritorio.yml` (all entity_id references)

## 🔄 Running as a Service

To run the proxy server automatically on boot, create a systemd service:

```bash
sudo nano /etc/systemd/system/album-art-proxy.service
```

```ini
[Unit]
Description=ESPHome Album Art Proxy
After=network.target

[Service]
Type=simple
User=your-username
WorkingDirectory=/path/to/esphome-devices
ExecStart=/path/to/esphome-devices/venv/bin/python album_art_proxy.py
Restart=always

[Install]
WantedBy=multi-user.target
```

Then:
```bash
sudo systemctl enable album-art-proxy.service
sudo systemctl start album-art-proxy.service
```

## 📊 Features

- ✅ Automatic album art fetching from Home Assistant
- ✅ Image resizing optimized for ESP32
- ✅ Caching for improved performance
- ✅ Placeholder image when no album art available
- ✅ Real-time updates when media changes
- ✅ Touch controls for media playback
- ✅ Volume control with visual feedback
- ✅ Progress bar with time display

## 🤝 Contributing

Feel free to modify the code for your specific needs. The proxy server is designed to be easily extensible for different image sources or processing requirements.
