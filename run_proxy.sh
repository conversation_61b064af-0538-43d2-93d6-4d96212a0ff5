#!/bin/bash

# Run the Album Art Proxy Server

echo "🎵 Starting Album Art Proxy Server..."

# Check if virtual environment exists
if [ ! -d "venv" ]; then
    echo "❌ Virtual environment not found. Please run ./setup_album_art.sh first"
    exit 1
fi

# Activate virtual environment
source venv/bin/activate

# Get local IP address
LOCAL_IP=$(ifconfig | grep -Eo 'inet (addr:)?([0-9]*\.){3}[0-9]*' | grep -Eo '([0-9]*\.){3}[0-9]*' | grep -v '127.0.0.1' | head -1)

echo "🌐 Server will be accessible at:"
echo "   http://localhost:5052"
echo "   http://$LOCAL_IP:5052"
echo ""
echo "📡 Update your ESPHome config with:"
echo "   http://$LOCAL_IP:5052/album_art"
echo ""
echo "🔍 Test endpoints:"
echo "   Status: http://$LOCAL_IP:5052/status"
echo "   Album Art: http://$LOCAL_IP:5052/album_art"
echo ""
echo "Press Ctrl+C to stop the server"
echo ""

# Run the proxy
python3 album_art_proxy.py
