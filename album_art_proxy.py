#!/usr/bin/env python3
"""
Album Art Proxy for ESPHome Media Screen

This script fetches album art from Home Assistant media players,
resizes it for ESP32 displays, and serves it via HTTP.

Requirements:
- pip install requests pillow flask homeassistant-api

Usage:
- Configure your Home Assistant URL and access token
- Run: python album_art_proxy.py
- ESPHome device will fetch resized images from http://YOUR_SERVER_IP:5052/album_art
"""

import os
import sys
import time
import logging
import hashlib
from io import BytesIO
from flask import Flask, Response, jsonify
from PIL import Image, ImageDraw
import requests
from threading import Thread, Lock
import json

# Configuration
HA_URL = "http://consuela-ha:8123"  # Change to your HA URL
HA_TOKEN = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiI3MDJmZTE5Y2Q1MjY0MGY4YmQ0M2NkNjBjN2YxYzBkYSIsImlhdCI6MTc1NjgzMTQ0NiwiZXhwIjoyMDcyMTkxNDQ2fQ.bOjMXpVDB0eYVzT0COQ5mpeUUN_49PvkEO_4sXZCMkY"   # Change to your token
MEDIA_PLAYER_ENTITY = "media_player.escritorio"
IMAGE_SIZE = (80, 80)  # Size for ESP32 display
CACHE_DURATION = 300   # Cache images for 5 minutes
SERVER_PORT = 5052

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = Flask(__name__)

class AlbumArtCache:
    def __init__(self):
        self.cache = {}
        self.cache_lock = Lock()
        
    def get_cache_key(self, entity_id, image_url):
        """Generate cache key from entity and image URL"""
        if not image_url:
            return None
        return hashlib.md5(f"{entity_id}_{image_url}".encode()).hexdigest()
    
    def get_cached_image(self, cache_key):
        """Get cached image if still valid"""
        with self.cache_lock:
            if cache_key in self.cache:
                cached_data, timestamp = self.cache[cache_key]
                if time.time() - timestamp < CACHE_DURATION:
                    return cached_data
                else:
                    del self.cache[cache_key]
        return None
    
    def cache_image(self, cache_key, image_data):
        """Cache processed image data"""
        with self.cache_lock:
            self.cache[cache_key] = (image_data, time.time())
            # Clean old entries
            current_time = time.time()
            expired_keys = [k for k, (_, ts) in self.cache.items() 
                          if current_time - ts > CACHE_DURATION]
            for key in expired_keys:
                del self.cache[key]

cache = AlbumArtCache()

def get_ha_headers():
    """Get headers for Home Assistant API requests"""
    return {
        "Authorization": f"Bearer {HA_TOKEN}",
        "Content-Type": "application/json"
    }

def get_media_player_state():
    """Get current media player state from Home Assistant"""
    try:
        url = f"{HA_URL}/api/states/{MEDIA_PLAYER_ENTITY}"
        response = requests.get(url, headers=get_ha_headers(), timeout=10)
        response.raise_for_status()
        return response.json()
    except Exception as e:
        logger.error(f"Error getting media player state: {e}")
        return None

def fetch_and_resize_image(image_url):
    """Fetch image from URL and resize for ESP32"""
    try:
        # Handle Home Assistant local URLs
        if image_url.startswith('/api/media_player_proxy/'):
            image_url = f"{HA_URL}{image_url}"
        
        headers = {}
        if image_url.startswith(HA_URL):
            headers = get_ha_headers()
        
        response = requests.get(image_url, headers=headers, timeout=15)
        response.raise_for_status()
        
        # Open and process image
        img = Image.open(BytesIO(response.content))
        
        # Convert to RGB if necessary
        if img.mode != 'RGB':
            img = img.convert('RGB')
        
        # Resize maintaining aspect ratio
        img.thumbnail(IMAGE_SIZE, Image.Resampling.LANCZOS)
        
        # Create a square image with padding if needed
        square_img = Image.new('RGB', IMAGE_SIZE, (0, 0, 0))
        paste_x = (IMAGE_SIZE[0] - img.width) // 2
        paste_y = (IMAGE_SIZE[1] - img.height) // 2
        square_img.paste(img, (paste_x, paste_y))
        
        # Convert to bytes for ESP32 (RGB565 format would be ideal, but RGB888 works)
        output = BytesIO()
        square_img.save(output, format='JPEG', quality=85, optimize=True)
        return output.getvalue()
        
    except Exception as e:
        logger.error(f"Error processing image {image_url}: {e}")
        return None

def create_placeholder_image():
    """Create a placeholder image when no album art is available"""
    img = Image.new('RGB', IMAGE_SIZE, (64, 64, 64))
    draw = ImageDraw.Draw(img)
    
    # Draw a simple music note placeholder
    center_x, center_y = IMAGE_SIZE[0] // 2, IMAGE_SIZE[1] // 2
    draw.ellipse([center_x-15, center_y-5, center_x+15, center_y+25], fill=(128, 128, 128))
    draw.rectangle([center_x+10, center_y-20, center_x+15, center_y+5], fill=(128, 128, 128))
    
    output = BytesIO()
    img.save(output, format='JPEG', quality=85)
    return output.getvalue()

@app.route('/album_art')
def get_album_art():
    """Endpoint to get current album art"""
    try:
        # Get current media player state
        state = get_media_player_state()
        if not state:
            logger.warning("Could not get media player state")
            placeholder = create_placeholder_image()
            response = Response(placeholder, mimetype='image/jpeg')
            # Disable caching for dynamic content
            response.headers['Cache-Control'] = 'no-cache, no-store, must-revalidate'
            response.headers['Pragma'] = 'no-cache'
            response.headers['Expires'] = '0'
            return response

        # Extract album art URL and media info for cache key
        attributes = state.get('attributes', {})
        image_url = attributes.get('entity_picture')
        media_title = attributes.get('media_title', '')
        media_artist = attributes.get('media_artist', '')

        # Create cache key based on media info, not just image URL
        media_key = f"{media_title}_{media_artist}_{image_url}"
        cache_key = cache.get_cache_key(MEDIA_PLAYER_ENTITY, media_key)

        if not image_url:
            logger.info("No album art available")
            placeholder = create_placeholder_image()
            response = Response(placeholder, mimetype='image/jpeg')
            response.headers['Cache-Control'] = 'no-cache, no-store, must-revalidate'
            response.headers['Pragma'] = 'no-cache'
            response.headers['Expires'] = '0'
            return response

        # Check cache first
        if cache_key:
            cached_image = cache.get_cached_image(cache_key)
            if cached_image:
                logger.info(f"Serving cached album art for: {media_title}")
                response = Response(cached_image, mimetype='image/jpeg')
                response.headers['Cache-Control'] = 'no-cache, no-store, must-revalidate'
                response.headers['Pragma'] = 'no-cache'
                response.headers['Expires'] = '0'
                return response

        # Fetch and process new image
        logger.info(f"Fetching album art for '{media_title}' by '{media_artist}': {image_url}")
        image_data = fetch_and_resize_image(image_url)

        if image_data:
            # Cache the processed image
            if cache_key:
                cache.cache_image(cache_key, image_data)
            response = Response(image_data, mimetype='image/jpeg')
            response.headers['Cache-Control'] = 'no-cache, no-store, must-revalidate'
            response.headers['Pragma'] = 'no-cache'
            response.headers['Expires'] = '0'
            return response
        else:
            placeholder = create_placeholder_image()
            response = Response(placeholder, mimetype='image/jpeg')
            response.headers['Cache-Control'] = 'no-cache, no-store, must-revalidate'
            response.headers['Pragma'] = 'no-cache'
            response.headers['Expires'] = '0'
            return response

    except Exception as e:
        logger.error(f"Error in album_art endpoint: {e}")
        placeholder = create_placeholder_image()
        response = Response(placeholder, mimetype='image/jpeg')
        response.headers['Cache-Control'] = 'no-cache, no-store, must-revalidate'
        response.headers['Pragma'] = 'no-cache'
        response.headers['Expires'] = '0'
        return response

@app.route('/status')
def get_status():
    """Health check endpoint"""
    state = get_media_player_state()
    if state:
        attributes = state.get('attributes', {})
        return jsonify({
            'status': 'ok',
            'media_player': MEDIA_PLAYER_ENTITY,
            'state': state.get('state'),
            'title': attributes.get('media_title'),
            'artist': attributes.get('media_artist'),
            'has_album_art': bool(attributes.get('entity_picture'))
        })
    else:
        return jsonify({'status': 'error', 'message': 'Cannot connect to Home Assistant'}), 500

if __name__ == '__main__':
    # Validate configuration
    if HA_TOKEN == "YOUR_LONG_LIVED_ACCESS_TOKEN":
        print("ERROR: Please configure your Home Assistant access token in the script")
        sys.exit(1)
    
    print(f"Starting Album Art Proxy Server...")
    print(f"Home Assistant URL: {HA_URL}")
    print(f"Media Player Entity: {MEDIA_PLAYER_ENTITY}")
    print(f"Image Size: {IMAGE_SIZE}")
    print(f"Server will run on: http://0.0.0.0:{SERVER_PORT}")
    print(f"Album art endpoint: http://YOUR_SERVER_IP:{SERVER_PORT}/album_art")
    print(f"Status endpoint: http://YOUR_SERVER_IP:{SERVER_PORT}/status")
    
    app.run(host='0.0.0.0', port=SERVER_PORT, debug=False)
